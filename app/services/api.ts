
// @/services/api.ts
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api/project';
const USE_PROXY = process.env.NEXT_PUBLIC_USE_PROXY === 'true';

// 统一响应格式接口
interface ApiResponse<T = any> {
    code: number;
    message: string;
    data: T;
}

// A generic fetch function to reduce boilerplate
async function fetchData(endpoint: string) {
    try {
        let url;
        if (USE_PROXY) {
            // 使用代理路由
            url = `/api/proxy?endpoint=${encodeURIComponent(endpoint)}`;
        } else {
            // 直接访问API
            url = `${API_BASE_URL}/${endpoint}`;
        }

        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const apiResponse: ApiResponse = await response.json();
        console.log(`API Response for ${endpoint}:`, apiResponse); // 添加调试日志

        // 检查API响应状态
        if (apiResponse.code !== 200) {
            console.error(`API Error for ${endpoint}:`, apiResponse.message);
            return null;
        }

        // 返回实际的业务数据
        return apiResponse.data;
    } catch (error) {
        console.error(`Failed to fetch ${endpoint}:`, error);
        return null;
    }
}

export const getProjectInfo = (id: string) => fetchData(`getPrjInfo/${id}`);
export const getRepaymentInfo = (id: string) => fetchData(`repayment/${id}`);
export const getDigitalAssets = (id: string) => fetchData(`attachments/${id}`);
export const getWorkflows = (id: string) => fetchData(`workflows/${id}`);
export const getTaskAssignment = (id: string) => fetchData(`task-assignment/${id}`);
export const getRelatedFees = (id: string) => fetchData(`fees/${id}`);
export const getProjectProgress = (id: string) => fetchData(`progress/${id}`);
