// zdhj-prjboard-web/app/components/ProjectProgress.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { getProjectProgress } from '@/services/api';

interface ProjectProgressProps {
  projectId: string;
}

const ProjectProgress: React.FC<ProjectProgressProps> = ({ projectId }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getProjectProgress(projectId).then(response => {
        if (response) {
          // 根据API文档转换字段结构
          const transformedData = {
            // 确保进度值在0-100范围内
            totalProgress: Math.round((response.prjProgress || 0) * (response.prjProgress <= 1 ? 100 : 1)),
            tasks: response.taskList?.map((task: any) => ({
              taskName: task.taskName,
              // 确保任务进度值在0-100范围内
              progress: Math.round((task.taskProgress || 0) * (task.taskProgress <= 1 ? 100 : 1)),
              status: "任务进度", // 后端没有返回状态，设置默认值
              taskIndex: task.taskIndex,
              link: task.link
            })) || []
          };
          setData(transformedData);
        }
        setLoading(false);
      });
    }
  }, [projectId]);

  if (loading) return (
    <div className="module">
      <div className="module-header">
        <h1 className="module-title">项目进度</h1>
      </div>
      <div className="module-content">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <div className="loading-text">正在加载项目进度...</div>
          <div className="loading-skeleton">
            <div className="skeleton-item skeleton-total"></div>
            <div className="skeleton-item skeleton-task"></div>
            <div className="skeleton-item skeleton-task"></div>
            <div className="skeleton-item skeleton-task"></div>
          </div>
        </div>
      </div>
    </div>
  );

  if (!data) return (
    <div className="module">
      <div className="module-header">
        <h1 className="module-title">项目进度</h1>
      </div>
      <div className="module-content">
        <div className="error-container">
          <div className="error-icon">⚠️</div>
          <div className="error-text">无法加载项目进度数据</div>
          <div className="error-subtitle">请检查网络连接或稍后重试</div>
        </div>
      </div>
    </div>
  );

  const getBarClass = (status: string) => {
    switch (status) {
      case '任务进度':
        return 'bar-normal';
      default:
        return 'bar-total';
    }
  };

  const getProgressStatus = (progress: number) => {
    if (progress === 0) return { text: '未开始', class: 'status-not-started', icon: '⏸️' };
    if (progress < 30) return { text: '刚开始', class: 'status-started', icon: '🚀' };
    if (progress < 70) return { text: '进行中', class: 'status-in-progress', icon: '⚡' };
    if (progress < 100) return { text: '接近完成', class: 'status-near-complete', icon: '🎯' };
    return { text: '已完成', class: 'status-complete', icon: '✅' };
  };

  return (
      <div className="module">
        <div className="module-header">
          <h1 className="module-title">项目进度</h1>
          <div className="chart-legend">
            <div className="legend-item">
              <div className="legend-color legend-total"></div>
              <span>项目总进度</span>
            </div>
            <div className="legend-item">
              <div className="legend-color legend-normal"></div>
              <span>任务进度</span>
            </div>

          </div>
        </div>

        <div className="module-content">
          <div className="chart-body" role="main" aria-label="项目进度图表">
            {/* Project Total Progress */}
            <div className="chart-grid total-progress-grid" role="group" aria-labelledby="total-progress-label">
              <div className="row-label total-label" id="total-progress-label">
                <span className="label-icon" aria-hidden="true">📊</span>
                <span>项目总进度</span>
              </div>
              <div className="progress-container total-container" role="progressbar"
                   aria-valuenow={data.totalProgress}
                   aria-valuemin={0}
                   aria-valuemax={100}
                   aria-label={`项目总进度 ${data.totalProgress}%`}>
                <div
                    className="progress-bar bar-total"
                    style={{
                      width: `${Math.min(data.totalProgress, 100)}%`,
                      '--final-width': `${Math.min(data.totalProgress, 100)}%`
                    }}
                    title={`项目总进度: ${data.totalProgress}%`}
                >
                  <span className="progress-text" aria-hidden="true">{data.totalProgress}%</span>
                </div>
              </div>
              <div className="percentage-scale total-percentage" aria-hidden="true">{data.totalProgress}%</div>
            </div>

            {/* Task Progress Bars */}
            <section className="tasks-section" aria-labelledby="tasks-section-title">
              <header className="tasks-header">
                <h2 className="tasks-title" id="tasks-section-title">任务进度详情</h2>
                <span className="tasks-count" aria-label={`共${data.tasks?.length || 0}个任务`}>
                  ({data.tasks?.length || 0}个任务)
                </span>
              </header>
              {data.tasks && data.tasks.map((task: any, index: number) => (
                  <div className="chart-grid task-progress-grid"
                       key={index}
                       role="group"
                       aria-labelledby={`task-label-${index}`}
                       tabIndex={0}
                       onKeyDown={(e) => {
                         if (e.key === 'Enter' || e.key === ' ') {
                           if (task.link) {
                             window.open(task.link, '_blank');
                           }
                         }
                       }}>
                    <div className="row-label task-label"
                         id={`task-label-${index}`}
                         title={task.taskName}>
                      <span className="task-index" aria-hidden="true">{index + 1}</span>
                      <span className="task-name">
                        {task.taskName.length > 8 ? `${task.taskName.substring(0, 8)}...` : task.taskName}
                      </span>
                    </div>
                    <div className="progress-container task-container"
                         role="progressbar"
                         aria-valuenow={task.progress}
                         aria-valuemin={0}
                         aria-valuemax={100}
                         aria-label={`${task.taskName} 进度 ${task.progress}%`}>
                      <div
                          className={`progress-bar ${getBarClass(task.status)}`}
                          style={{
                            width: `${Math.min(task.progress, 100)}%`,
                            '--final-width': `${Math.min(task.progress, 100)}%`
                          }}
                          title={`${task.taskName}: ${task.progress}%`}
                      >
                        <span className="progress-text" aria-hidden="true">{task.progress}%</span>
                      </div>
                    </div>
                    <div className="percentage-scale task-percentage" aria-hidden="true">{task.progress}%</div>
                    {task.link && (
                      <div className="task-link-indicator" title="点击查看详情" aria-label="有链接可查看详情">
                        🔗
                      </div>
                    )}
                  </div>
              ))}
            </div>
          </div>
        </div>
      </div>
  );
};

export default ProjectProgress;