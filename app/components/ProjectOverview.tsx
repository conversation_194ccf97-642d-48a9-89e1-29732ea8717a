// zdhj-prjboard-web/app/components/ProjectOverview.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { getProjectInfo } from '@/services/api';

interface ProjectOverviewProps {
  projectId: string;
}

const ProjectOverview: React.FC<ProjectOverviewProps> = ({ projectId }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getProjectInfo(projectId).then(response => {
        if (response) {
          // 根据API文档转换字段名
          const transformedData = {
            prjName: response.projectName || "未知项目",
            prjStatus: response.projectStatus || "状态未知",
            prjInfo: response.projectOverview || "项目概况未知",
            entrustUnit: response.clientName || "委托方未知",
            capitalSourceName: response.fundingSource || "资金来源未知",
            investedAmount: response.investmentAmount || 0,
            prjBldArea: response.buildingArea || 0,
            isWholeProcess: response.isFullConsulting || "",
            bizTypeName: response.businessTypeLevel2 || response.businessTypeLevel1 || "",
            firstResourcer: response.firstResource || ""
          };
          setData(transformedData);
        }
        setLoading(false);
      });
    }
  }, [projectId]);

  if (loading) return <div className="project-card">加载中...</div>;
  if (!data) return <div className="project-card">无法加载项目概况</div>;

  return (
    <div 
      className="module"

    >
      <div className="header"
           style={{
             backgroundImage: "url('/img/lan.png')",
             backgroundRepeat: 'no-repeat',
             backgroundSize: '100% 100%'
           }}

      >


      <div className="project-header p-4 w-full text-white">
        <img src="/img/xmtp.png" alt="Project Thumbnail" className="w-full float-none mr-0" />
        <div className="project-title">{data.prjName}</div>
        <div className="project-info">{data.prjInfo}</div>
        <span className="status-badge bg-[#52c41a] text-white py-1 px-3 rounded-[20px] text-[12px] font-medium inline-block">{data.prjStatus}</span>
      </div>
      </div>
      <div className="project-content">
        <img src="/img/icon.png" alt="Icon" />
        <h3 className="project-section-title">基本信息</h3>

        <div className="info-list">
          <div className="info-item">
            <span className="info-label">资金来源:</span>
            <span className="info-value">{data.capitalSourceName}</span>
          </div>
          <div className="info-item">
            <span className="info-label  " >委托方:</span>
            <span className="info-value">{data.entrustUnit}</span>
          </div>
          <div className="info-item">
            <span className="info-label">投资金额(万元):</span>
            <span className="info-value">{data.investedAmount}</span>
          </div>
          <div className="info-item">
            <span className="info-label">建筑面积(㎡):</span>
            <span className="info-value">{data.prjBldArea}</span>
          </div>
          <div className="info-item">
            <span className="info-label">全过程工程咨询:</span>
            <span className="info-value">{data.isWholeProcess}</span>
          </div>
          <div className="info-item">
            <span className="info-label">业务类型:</span>
            <span className="info-value">{data.bizTypeName}</span>
          </div>
          <div className="info-item">
            <span className="info-label">第一资源人:</span>
            <span className="info-value">{data.firstResourcer}</span>
          </div>
        </div>
      </div>

        </div>
  );
};

export default ProjectOverview;