// zdhj-prjboard-web/app/components/ProjectMilestones.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { getDigitalAssets } from '@/services/api';

interface ProjectMilestonesProps {
  projectId: string;
}

interface FileData {
  title: string;
  url: string;
  uploadDate: string;
}

interface MilestoneData {
  title: string;
  files: FileData[];
}

const ProjectMilestones: React.FC<ProjectMilestonesProps> = ({ projectId }) => {
  const [data, setData] = useState<MilestoneData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getDigitalAssets(projectId).then(response => {
        if (response && response.DigitalAsset) {
          // 根据API文档处理DigitalAsset数组结构
          const timelineData: MilestoneData[] = response.DigitalAsset
            .sort((a: any, b: any) => (a.order || 0) - (b.order || 0)) // 按order排序
            .map((item: any) => ({
              title: item.workflowName,
              files: item.attachments?.map((attachment: any) => ({
                title: attachment.fileName,
                url: attachment.url,
                uploadDate: attachment.uploadDate
              })) || []
            }));

          setData(timelineData);
        }
        setLoading(false);
      });
    }
  }, [projectId]);

  if (loading) return <div className="module">加载中...</div>;
  if (!data || data.length === 0) return <div className="module">无里程碑数据</div>;

  return (
    <div className="module">
      <div className="module-header">
        <h1 className="module-title">里程碑/数字资产</h1>
      </div>

      <div className="module-content">
        <div className="timeline-container">
          <div className="timeline">
            {data.map((milestone, index) => (
              <div className="timeline-node" key={index}>
                <div className="node-indicator">
                  <div className="node-dot"></div>
                  {index < data.length - 1 && <div className="node-line"></div>}
                </div>
                <div className="node-content">
                  <div className="node-header">
                    <div className="node-title">
                      <span className="milestone-icon">🎯</span>
                      {milestone.title}
                      <span className="file-count">({milestone.files.length}个文件)</span>
                    </div>
                  </div>
                  <div className="files-container">
                    {milestone.files.map((file, fileIndex) => (
                      <div className="file-card" key={fileIndex}>
                        <div className="file-content">
                          <div className="file-header">
                            <div className="file-title">
                              <span className="file-icon">📄</span>
                              <a href={file.url} target="_blank" rel="noopener noreferrer" className="file-link">
                                {file.title}
                              </a>
                            </div>
                            {file.uploadDate && (
                              <div className="upload-date">
                                <span className="date-icon">📅</span>
                                {file.uploadDate}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectMilestones;