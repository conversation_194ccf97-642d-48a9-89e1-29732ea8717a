// zdhj-prjboard-web/app/components/ProjectRelatedFees.tsx
"use client";

import React, { useEffect, useRef, useState } from 'react';
import { getRelatedFees } from '@/services/api';

interface ProjectRelatedFeesProps {
  projectId: string;
}

const ProjectRelatedFees: React.FC<ProjectRelatedFeesProps> = ({ projectId }) => {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // 格式化金额显示
  const formatAmount = (amount: number) => {
    if (amount >= 10000) {
      return `${(amount / 10000).toFixed(1)}万`;
    }
    return amount.toLocaleString();
  };

  useEffect(() => {
    if (projectId) {
      getRelatedFees(projectId).then(response => {
        if (response && response.relevantFees) {
          // 根据API文档处理relevantFees数组结构
          const totalFees = response.relevantFees.reduce((sum: number, fee: any) => sum + (fee.feeAmount || 0), 0) || 0;
          const transformedData = {
            totalFees: totalFees,
            feeDetails: response.relevantFees.map((fee: any) => ({
              feeName: fee.feeType,
              amount: fee.feeAmount || 0,
              percentage: fee.percentage || 0
            })) || []
          };
          setData(transformedData);
        }
        setLoading(false);
      });
    }
  }, [projectId]);

  useEffect(() => {
    if (data && chartRef.current && (window as any).Chart) {
      const ctx = chartRef.current.getContext('2d');
      if (ctx) {
        let chartStatus = (window as any).Chart.getChart(chartRef.current);
        if (chartStatus != undefined) {
          chartStatus.destroy();
        }

        const { Chart } = (window as any);
        const feeLabels = data.feeDetails.map((fee: any) => fee.feeName);
        const feeValues = data.feeDetails.map((fee: any) => fee.amount);

        // 定义费用类型与颜色的映射关系
        const feeColorMap: { [key: string]: string } = {
          '差旅费（市场）': '#05c350',
          '差旅费（执业）': '#ff4445',
          '市场费用报销': '#7e53c6',
          '执业费用报销': '#fe7e2e',
          '市场提奖': '#33c2fa',
          '执业提奖': '#4075ef'
        };

        // 根据费用名称获取对应颜色
        const backgroundColors = feeLabels.map((label: string) => feeColorMap[label] || '#cccccc');

        new Chart(ctx, {
          type: 'doughnut',
          data: {
            labels: feeLabels,
            datasets: [{
              data: feeValues,
              backgroundColor: backgroundColors,
              borderWidth: 0,
              hoverOffset: 15
            }]
          },
          options: {
            cutout: '65%',
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                callbacks: {
                  label: function(context: any) {
                    const value = context.raw;
                    const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                    const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                    return `${context.label}: ${value}元 (${percentage}%)`;
                  }
                }
              }
            }
          }
        });
      }
    }
  }, [data]);

  if (loading) return <div className="column column-2">加载中...</div>;
  if (!data) return <div className="column column-2">无法加载相关费用信息</div>;

  const feeIconMap: { [key: string]: string } = {
    '市场提奖': '/img/sctj.png',
    '市场费用报销': '/img/scfybx.png',
    '差旅费（市场）': '/img/clfsc.png',
    '执业提奖': '/img/zytj.png',
    '执业费用报销': '/img/zyfybx.png',
    '差旅费（执业）': '/img/clfzy.png',
  };

  // 费用类型与颜色的映射关系（用于图例显示）
  const feeColorMap: { [key: string]: string } = {
    '差旅费（市场）': '#05c350',
    '差旅费（执业）': '#ff4445',
    '市场费用报销': '#7e53c6',
    '执业费用报销': '#fe7e2e',
    '市场提奖': '#33c2fa',
    '执业提奖': '#4075ef'
  };

  return (
    <div className="module">
      <div className="module-header">
        <h1 className="module-title">相关费用</h1>
      </div>

      <div className="module-content">
        <div className="expense-content">
          <div className="expense-chart-container">
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '80px' }}>
              <div className="expense-chart-wrapper">
                <canvas ref={chartRef} id="expensePieChart"></canvas>
                <div className="expense-chart-center">
                  <div className="expense-total-amount">{formatAmount(data.totalFees)}</div>
                  <div className="expense-total-label">总费用</div>
                </div>
              </div>
              
              {/* 添加图例在饼图右侧 */}
              <div className="expense-chart-legend">
                {data.feeDetails && data.feeDetails.map((fee: any, index: number) => (
                  <div className="legend-item" key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: '5px' }}>
                    <div 
                      className="legend-color-box" 
                      style={{ 
                        width: '12px', 
                        height: '12px', 
                        backgroundColor: feeColorMap[fee.feeName] || '#cccccc',
                        marginRight: '8px',
                        borderRadius: '2px'
                      }}
                    ></div>
                    <div className="legend-label" style={{ fontSize: '12px', color: '#333' }}>
                      {fee.feeName}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="expense-details-container">
            <div className="expense-grid" style={{ 
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '8px'
            }}>
              {data.feeDetails && data.feeDetails.map((fee: any, index: number) => (
                <div className="expense-detail-item" key={index}>
                  <div className="expense-detail-icon">
                    <img src={feeIconMap[fee.feeName] || '/img/icon.png' } alt={fee.feeName} />
                  </div>
                  <div className="expense-detail-info">
                    <div className="expense-detail-name" title={fee.feeName} style={{
                      whiteSpace: 'normal',
                      wordBreak: 'break-all',
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      lineHeight: '1.2em',
                      maxHeight: '2.4em'
                    }}>
                      {fee.feeName}
                    </div>
                    <div className="expense-detail-amount">{formatAmount(fee.amount)}元</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectRelatedFees;