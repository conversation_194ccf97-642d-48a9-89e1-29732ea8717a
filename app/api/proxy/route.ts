import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const endpoint = searchParams.get('endpoint');

  if (!endpoint) {
    return new Response('Missing endpoint parameter', { status: 400 });
  }

  try {
    const apiBaseUrl = process.env.API_BASE_URL || 'http://localhost:8080/api/project';
    const response = await fetch(`${apiBaseUrl}/${endpoint}`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return new Response(JSON.stringify(data), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  } catch (error) {
    console.error('Proxy error:', error);
    return new Response('Failed to fetch data', { status: 500 });
  }
}