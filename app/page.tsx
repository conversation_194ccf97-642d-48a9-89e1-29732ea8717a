import ProjectOverview from './components/ProjectOverview';
import ProjectRepayment from './components/ProjectRepayment';
import ProjectTaskAssignment from './components/ProjectTaskAssignment';
import ProjectMilestones from './components/ProjectMilestones';
import ProjectWorkflows from './components/ProjectWorkflows';
import ProjectRelatedFees from './components/ProjectRelatedFees';
import ProjectProgress from './components/ProjectProgress';

export default function Home() {
  return (
    <div className="main-container">
      {/* 导航栏 - 8vh */}
      <div className="nav-container">
        <nav className="navbar">
          {/* Logo区域 */}
          <div className="logo">
            <img src="/img/logo.png" alt="Logo" />
          </div>


        </nav>
      </div>

      {/* 主内容区域 - 92vh */}
      <div className="main-content">
        {/* 上方四个板块 - 35vh */}
        <div className="top-row">
          <ProjectOverview projectId="2369" />
          <ProjectRepayment projectId="2369" />
          <ProjectTaskAssignment projectId="2369" />
          <ProjectMilestones projectId="2369" />
        </div>

        {/* 下方三个板块 - 46.5vh */}
        <div className="bottom-row">
          <ProjectWorkflows projectId="2369" />
          <ProjectRelatedFees projectId="2369" />
          <ProjectProgress projectId="2369" />
        </div>
      </div>
    </div>
  );
}