// zdhj-prjboard-web/app/components/ProjectProgress.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { getProjectProgress } from '@/services/api';

interface ProjectProgressProps {
  projectId: string;
}

const ProjectProgress: React.FC<ProjectProgressProps> = ({ projectId }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getProjectProgress(projectId).then(response => {
        if (response) {
          // 根据API文档转换字段结构
          const transformedData = {
            // 确保进度值在0-100范围内
            totalProgress: Math.round((response.prjProgress || 0) * (response.prjProgress <= 1 ? 100 : 1)),
            tasks: response.taskList?.map((task: any) => ({
              taskName: task.taskName,
              // 确保任务进度值在0-100范围内
              progress: Math.round((task.taskProgress || 0) * (task.taskProgress <= 1 ? 100 : 1)),
              status: "任务进度", // 后端没有返回状态，设置默认值
              taskIndex: task.taskIndex,
              link: task.link
            })) || []
          };
          setData(transformedData);
        }
        setLoading(false);
      });
    }
  }, [projectId]);

  if (loading) return <div className="column column-3">Loading...</div>;
  if (!data) return <div className="column column-3">Could not load project progress.</div>;

  const getBarClass = (status: string) => {
    switch (status) {
      case '任务进度':
        return 'bar-normal';
      default:
        return 'bar-total';
    }
  };

  return (
      <div className="module">
        <div className="module-header">
          <h1 className="module-title">项目进度</h1>
          <div className="chart-legend">
            <div className="legend-item">
              <div className="legend-color legend-total"></div>
              <span>项目总进度</span>
            </div>
            <div className="legend-item">
              <div className="legend-color legend-normal"></div>
              <span>任务进度</span>
            </div>

          </div>
        </div>

        <div className="module-content">
          <div className="chart-body">
            {/* Project Total Progress */}
            <div className="chart-grid total-progress-grid">
              <div className="row-label total-label">
                <span className="label-icon">📊</span>
                <span>项目总进度</span>
              </div>
              <div className="progress-container total-container">
                <div
                    className="progress-bar bar-total"
                    style={{ width: `${Math.min(data.totalProgress, 100)}%` }}
                >
                  <span className="progress-text">{data.totalProgress}%</span>
                </div>
                <div className="progress-glow"></div>
              </div>
              <div className="percentage-scale total-percentage">{data.totalProgress}%</div>
            </div>

            {/* Task Progress Bars */}
            <div className="tasks-section">
              <div className="tasks-header">
                <span className="tasks-title">任务进度详情</span>
                <span className="tasks-count">({data.tasks?.length || 0}个任务)</span>
              </div>
              {data.tasks && data.tasks.map((task: any, index: number) => (
                  <div className="chart-grid task-progress-grid" key={index}>
                    <div className="row-label task-label" title={task.taskName}>
                      <span className="task-index">{index + 1}</span>
                      <span className="task-name">
                    {task.taskName.length > 8 ? `${task.taskName.substring(0, 8)}...` : task.taskName}
                  </span>
                    </div>
                    <div className="progress-container task-container">
                      <div
                          className={`progress-bar ${getBarClass(task.status)}`}
                          style={{ width: `${Math.min(task.progress, 100)}%` }}
                      >
                        <span className="progress-text">{task.progress}%</span>
                      </div>
                      <div className="progress-track"></div>
                    </div>
                    <div className="percentage-scale task-percentage">{task.progress}%</div>
                  </div>
              ))}
            </div>
          </div>
        </div>
      </div>
  );
};

export default ProjectProgress;